import { SSML_CONFIG, VOICE_DEFAULTS } from "./config";

/**
 * Optimiza el texto para mejorar la síntesis de voz
 */
export function optimizeTextForSpeech(text: string): string {
  return text
    .replace(/([.!?])\s+/g, `$1 <break time="${SSML_CONFIG.BREAK_TIME}"/> `)
    .replace(/\b(Sí|Si)\b/g, '<emphasis level="moderate">Sí</emphasis>')
    .replace(/\b(No)\b/g, '<emphasis level="moderate">No</emphasis>')
    .replace(/\b(Tal vez|Quizás)\b/g, '<emphasis level="moderate">$1</emphasis>')
    .trim();
}

/**
 * Envuelve el texto en formato SSML
 */
export function wrapInSSML(text: string, voiceId: string): string {
  return `
  <speak version="${SSML_CONFIG.VERSION}" xmlns="${SSML_CONFIG.XMLNS}" xml:lang="${SSML_CONFIG.LANG}">
    <voice name="${voiceId}">
      <prosody rate="${VOICE_DEFAULTS.RATE}" pitch="${VOICE_DEFAULTS.PITCH}">
        ${text}
      </prosody>
    </voice>
  </speak>`.trim();
}

/**
 * Crea los parámetros de voz para la API de Azure
 */
export function createVoiceParams(voiceId: string) {
  return {
    voice_id: voiceId,
    rate: VOICE_DEFAULTS.RATE,
    pitch: VOICE_DEFAULTS.PITCH,
    volume: VOICE_DEFAULTS.VOLUME,
    style: VOICE_DEFAULTS.STYLE
  };
}

/**
 * Valida que el texto sea apropiado para síntesis
 */
export function validateTextForSpeech(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  const trimmed = text.trim();
  if (trimmed.length === 0) {
    return false;
  }

  // Verificar que no sea solo espacios en blanco o caracteres especiales
  if (!/[a-zA-ZáéíóúñÁÉÍÓÚÑ0-9]/.test(trimmed)) {
    return false;
  }

  return true;
}

/**
 * Limpia el texto de caracteres problemáticos para SSML
 */
export function sanitizeTextForSSML(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}
