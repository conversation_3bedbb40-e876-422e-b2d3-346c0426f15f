import { log } from "../LogService";
import { AzureVoicesService } from "./azureVoicesService";
import type { IVoicesService } from "../../models/speech";

/**
 * Servicio principal de Speech - Maneja la generación de audio desde texto
 * Solo se encarga de la comunicación con Azure TTS, no de la reproducción
 */
export class SpeechService {
  private azureService: IVoicesService;
  private serviceName = "speechService";

  // ========== CONSTRUCTOR E INICIALIZACIÓN ==========
  constructor() {
    this.azureService = AzureVoicesService.getInstance();
    log.debug(this.serviceName, "🎤 SpeechService inicializado (solo generación)");
  }

  // ========== GETTERS Y ESTADO ==========

  public getCurrentVoiceId(): string {
    return this.azureService.getCurrentVoiceId();
  }

  public getAvailableVoicesList(): string[] {
    return this.azureService.getAvailableVoicesList();
  }

  // ========== CONFIGURACIÓN DE VOZ ==========
  public async configVoice(genre: string): Promise<boolean> {
    try {
      log.info(this.serviceName, `🔧 Configurando voz: ${genre}`);
      const success = await this.azureService.configVoice(genre);

      if (success) {
        const voiceId = this.azureService.getCurrentVoiceId();
        log.success(this.serviceName, `✅ Voz configurada: ${voiceId}`);
      } else {
        log.warn(this.serviceName, "❌ No se pudo configurar la voz");
      }

      return success;
    } catch (error) {
      log.error(this.serviceName, "Error configurando voz", error);
      return false;
    }
  }

  // ========== API PRINCIPAL DE GENERACIÓN ==========
  public async getAudio(message: string): Promise<Blob> {
    try {
      log.debug(this.serviceName, `🎤 Generando audio: ${message.substring(0, 50)}...`);
      const audioBlob = await this.azureService.getAudio(message);
      log.success(this.serviceName, `✅ Audio generado correctamente (${audioBlob.size} bytes)`);
      return audioBlob;
    } catch (error) {
      log.error(this.serviceName, "Error obteniendo audio", error);
      throw error;
    }
  }

  /**
   * @deprecated Usar getAudio() directamente y AudioManager.playSpeech() para reproducir
   * Este método se mantiene temporalmente para compatibilidad
   */
  public async getSpeech(message: string): Promise<string> {
    try {
      log.warn(this.serviceName, "⚠️ getSpeech() está deprecated, usar getAudio() + AudioManager");
      const audioBlob = await this.getAudio(message);
      const audioUrl = URL.createObjectURL(audioBlob);
      return audioUrl;
    } catch (error) {
      log.error(this.serviceName, "Error obteniendo URL de speech", error);
      throw error;
    }
  }

  // ========== MÉTODOS DE CONVENIENCIA PARA GENERACIÓN ==========

  /**
   * @deprecated Usar getAudio() directamente y AudioManager para reproducir
   * Este método se mantiene temporalmente para compatibilidad
   */
  public async speak(_text: string): Promise<void> {
    log.warn(this.serviceName, "⚠️ speak() está deprecated, usar getAudio() + AudioManager.playSpeech()");
    throw new Error("speak() está deprecated. Usar getAudio() + AudioManager.playSpeech()");
  }

  /**
   * @deprecated Usar getAudio() directamente y AudioManager para reproducir
   */
  public async toSpeech(_url: string): Promise<void> {
    log.warn(this.serviceName, "⚠️ toSpeech() está deprecated, usar AudioManager");
    throw new Error("toSpeech() está deprecated. Usar AudioManager.playSpeech()");
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public setSpeech(_url: string): void {
    log.warn(this.serviceName, "⚠️ setSpeech() está deprecated, usar AudioManager");
    throw new Error("setSpeech() está deprecated. Usar AudioManager");
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public async playSpeech(): Promise<void> {
    log.warn(this.serviceName, "⚠️ playSpeech() está deprecated, usar AudioManager");
    throw new Error("playSpeech() está deprecated. Usar AudioManager.playSpeech()");
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public stopSpeech(): void {
    log.warn(this.serviceName, "⚠️ stopSpeech() está deprecated, usar AudioManager");
    throw new Error("stopSpeech() está deprecated. Usar AudioManager.stopSpeech()");
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public noSpeech(): void {
    log.warn(this.serviceName, "⚠️ noSpeech() está deprecated, usar AudioManager");
    throw new Error("noSpeech() está deprecated. Usar AudioManager.stopSpeech()");
  }

  // ========== AUTO-CONFIGURACIÓN Y GENERACIÓN ==========
  public async generateAudioWithAutoConfig(text: string, genre: string = "female"): Promise<Blob> {
    try {
      // Auto-configurar si no está configurado
      if (!this.getCurrentVoiceId()) {
        log.info(this.serviceName, "🔧 Auto-configurando voz...");
        const configured = await this.configVoice(genre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      return await this.getAudio(text);
    } catch (error) {
      log.error(this.serviceName, "Error en generateAudioWithAutoConfig", error);
      throw error;
    }
  }

  /**
   * @deprecated Usar generateAudioWithAutoConfig() + AudioManager.playSpeech()
   */
  public async speakWithAutoConfig(_text: string, _genre: string = "female"): Promise<void> {
    log.warn(this.serviceName, "⚠️ speakWithAutoConfig() está deprecated, usar generateAudioWithAutoConfig() + AudioManager");
    throw new Error("speakWithAutoConfig() está deprecated. Usar generateAudioWithAutoConfig() + AudioManager.playSpeech()");
  }

  // ========== UTILIDADES Y LIMPIEZA ==========
  public cleanup(): void {
    this.azureService.cleanupCache();
    log.info(this.serviceName, "🧹 Servicio limpiado (solo cache de Azure)");
  }
}
