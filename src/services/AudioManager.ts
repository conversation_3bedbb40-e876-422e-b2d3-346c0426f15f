// services/AudioManager.ts


export type AudioType = "music" | "speech" | "sfx";

export interface AudioState {
  music: {
    isPlaying: boolean;
    volume: number;
    currentTrack: string | null;
  };
  speech: {
    isPlaying: boolean;
    volume: number;
  };
  sfx: {
    volume: number;
  };
  masterVolume: number;
  isMuted: boolean;
}

/**
 * Gestor de audio centralizado para música, narración y efectos
 * Permite control independiente de cada tipo de audio
 */
class AudioManager {
  private static instance: AudioManager;
  private musicAudio: HTMLAudioElement | null = null;
  private speechAudio: HTMLAudioElement | null = null;
  private serviceName = "audioManager";

  private state: AudioState = {
    music: {
      isPlaying: false,
      volume: 0.3, // Música más baja por defecto
      currentTrack: null,
    },
    speech: {
      isPlaying: false,
      volume: 0.8, // Narración más alta
    },
    sfx: {
      volume: 0.6,
    },
    masterVolume: 1.0,
    isMuted: false,
  };

  private listeners: Set<(state: AudioState) => void> = new Set();

  private constructor() {
    this.initializeAudioElements();
  }

  public static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  // ========== INICIALIZACIÓN ==========
  private initializeAudioElements() {
    // Elemento para música de fondo
    this.musicAudio = document.createElement("audio");
    this.musicAudio.id = "music-audio";
    this.musicAudio.loop = true;
    this.musicAudio.preload = "auto";
    this.musicAudio.volume = this.state.music.volume * this.state.masterVolume;
    document.body.appendChild(this.musicAudio);

    // Elemento para narración (speech)
    this.speechAudio = document.createElement("audio");
    this.speechAudio.id = "speech-audio";
    this.speechAudio.preload = "metadata";
    this.speechAudio.volume = this.state.speech.volume * this.state.masterVolume;
    document.body.appendChild(this.speechAudio);

    // Event listeners
    this.setupEventListeners();

    console.log(`✅ [${this.serviceName}] AudioManager inicializado correctamente`);
  }

  private setupEventListeners() {
    if (this.musicAudio) {
      this.musicAudio.addEventListener("play", () => {
        this.updateState({ music: { ...this.state.music, isPlaying: true } });
      });

      this.musicAudio.addEventListener("pause", () => {
        this.updateState({ music: { ...this.state.music, isPlaying: false } });
      });

      this.musicAudio.addEventListener("ended", () => {
        this.updateState({ music: { ...this.state.music, isPlaying: false } });
      });
    }

    if (this.speechAudio) {
      this.speechAudio.addEventListener("play", () => {
        this.updateState({ speech: { ...this.state.speech, isPlaying: true } });
        // Ducking: bajar música cuando habla
        this.duckMusic(true);
      });

      this.speechAudio.addEventListener("pause", () => {
        this.updateState({ speech: { ...this.state.speech, isPlaying: false } });
        this.duckMusic(false);
      });

      this.speechAudio.addEventListener("ended", () => {
        this.updateState({ speech: { ...this.state.speech, isPlaying: false } });
        this.duckMusic(false);
      });
    }
  }

  // ========== MÚSICA DE FONDO ==========
  public async playMusic(src: string): Promise<void> {
    if (!this.musicAudio) return;

    try {
      console.log(`ℹ️ [${this.serviceName}] 🎵 Reproduciendo música: ${src}`);

      this.musicAudio.src = src;
      this.musicAudio.currentTime = 0;

      await this.musicAudio.play();

      this.updateState({
        music: {
          ...this.state.music,
          currentTrack: src,
          isPlaying: true
        }
      });

      console.log(`✅ [${this.serviceName}] Música iniciada correctamente`);
    } catch (error) {
      // 🔧 CAMBIO: Manejar error de autoplay de forma más elegante
      if (error instanceof Error && error.name === 'NotAllowedError') {
        console.warn(`⚠️ [${this.serviceName}] ⚠️ Autoplay bloqueado - esperando interacción del usuario`);

        // Configurar la música pero no reproducir aún
        this.updateState({
          music: {
            ...this.state.music,
            currentTrack: src,
            isPlaying: false
          }
        });

        // Intentar reproducir cuando el usuario interactúe
        this.setupAutoplayRetry();
      } else {
        console.error(`❌ [${this.serviceName}] Error reproduciendo música`, error);
        throw error;
      }
    }
  }

  // 🆕 NUEVO: Retry automático cuando el usuario interactúe
  private setupAutoplayRetry(): void {
    const playOnInteraction = async () => {
      if (this.musicAudio && this.state.music.currentTrack && !this.state.music.isPlaying) {
        try {
          await this.musicAudio.play();
          this.updateState({
            music: {
              ...this.state.music,
              isPlaying: true
            }
          });
          console.log(`✅ [${this.serviceName}] ✅ Música iniciada tras interacción del usuario`);
        } catch (error) {
          console.warn(`⚠️ [${this.serviceName}] ⚠️ Aún no se puede reproducir música`);
        }
      }

      // Remover listeners después del primer intento
      ['click', 'touchstart', 'keydown'].forEach(event => {
        document.removeEventListener(event, playOnInteraction);
      });
    };

    // Escuchar próxima interacción
    ['click', 'touchstart', 'keydown'].forEach(event => {
      document.addEventListener(event, playOnInteraction, { once: true });
    });
  }

  public pauseMusic(): void {
    if (this.musicAudio && !this.musicAudio.paused) {
      this.musicAudio.pause();
      console.log(`ℹ️ [${this.serviceName}] 🎵 Música pausada`);
    }
  }

  public resumeMusic(): void {
    if (this.musicAudio && this.musicAudio.paused && this.state.music.currentTrack) {
      this.musicAudio.play().catch(error => {
        console.error(`❌ [${this.serviceName}] Error reanudando música`, error);
      });
      console.log(`ℹ️ [${this.serviceName}] 🎵 Música reanudada`);
    }
  }

  public stopMusic(): void {
    if (this.musicAudio) {
      this.musicAudio.pause();
      this.musicAudio.currentTime = 0;
      this.musicAudio.src = "";

      this.updateState({
        music: {
          ...this.state.music,
          isPlaying: false,
          currentTrack: null
        }
      });

      console.log(`ℹ️ [${this.serviceName}] 🎵 Música detenida`);
    }
  }

  // ========== NARRACIÓN ==========
  public async playSpeech(audioBlob: Blob): Promise<void> {
    if (!this.speechAudio) return;

    try {
      const audioUrl = URL.createObjectURL(audioBlob);

      console.log(`ℹ️ [${this.serviceName}] 🗣️ Reproduciendo narración`);

      this.speechAudio.src = audioUrl;
      this.speechAudio.currentTime = 0;

      await this.speechAudio.play();

      // Limpiar URL cuando termine
      this.speechAudio.addEventListener("ended", () => {
        URL.revokeObjectURL(audioUrl);
      }, { once: true });

      console.log(`✅ [${this.serviceName}] Narración iniciada correctamente`);
    } catch (error) {
      // 🔧 CAMBIO: Manejar error de autoplay para narración
      if (error instanceof Error && error.name === 'NotAllowedError') {
        console.warn(`⚠️ [${this.serviceName}] ⚠️ Autoplay de narración bloqueado - esperando interacción`);

        // Intentar reproducir cuando el usuario interactúe
        this.setupSpeechAutoplayRetry(audioBlob);
      } else {
        console.error(`❌ [${this.serviceName}] Error reproduciendo narración`, error);
        throw error;
      }
    }
  }

  // 🆕 NUEVO: Retry automático para narración
  private setupSpeechAutoplayRetry(audioBlob: Blob): void {
    const playOnInteraction = async () => {
      if (this.speechAudio) {
        try {
          const audioUrl = URL.createObjectURL(audioBlob);
          this.speechAudio.src = audioUrl;
          this.speechAudio.currentTime = 0;

          await this.speechAudio.play();

          this.speechAudio.addEventListener("ended", () => {
            URL.revokeObjectURL(audioUrl);
          }, { once: true });

          console.log(`✅ [${this.serviceName}] ✅ Narración iniciada tras interacción del usuario`);
        } catch (error) {
          console.warn(`⚠️ [${this.serviceName}] ⚠️ Aún no se puede reproducir narración`);
        }
      }

      // Remover listeners después del primer intento
      ['click', 'touchstart', 'keydown'].forEach(event => {
        document.removeEventListener(event, playOnInteraction);
      });
    };

    // Escuchar próxima interacción
    ['click', 'touchstart', 'keydown'].forEach(event => {
      document.addEventListener(event, playOnInteraction, { once: true });
    });
  }

  public pauseSpeech(): void {
    if (this.speechAudio && !this.speechAudio.paused) {
      this.speechAudio.pause();
      console.log(`ℹ️ [${this.serviceName}] 🗣️ Narración pausada`);
    }
  }

  public resumeSpeech(): void {
    if (this.speechAudio && this.speechAudio.paused) {
      this.speechAudio.play().catch(error => {
        console.error(`❌ [${this.serviceName}] Error reanudando narración`, error);
      });
      console.log(`ℹ️ [${this.serviceName}] 🗣️ Narración reanudada`);
    }
  }

  public stopSpeech(): void {
    if (this.speechAudio) {
      this.speechAudio.pause();
      this.speechAudio.currentTime = 0;

      this.updateState({
        speech: {
          ...this.state.speech,
          isPlaying: false
        }
      });

      console.log(`ℹ️ [${this.serviceName}] 🗣️ Narración detenida`);
    }
  }

  // ========== DUCKING (bajar música durante narración) ==========
  private duckMusic(duck: boolean): void {
    if (!this.musicAudio) return;

    const targetVolume = duck
      ? this.state.music.volume * 0.3 * this.state.masterVolume // Bajar al 30%
      : this.state.music.volume * this.state.masterVolume; // Volumen normal

    // Fade suave
    this.fadeAudioVolume(this.musicAudio, targetVolume, 500);
  }

  private fadeAudioVolume(audio: HTMLAudioElement, targetVolume: number, duration: number): void {
    const startVolume = audio.volume;
    const volumeDiff = targetVolume - startVolume;
    const steps = 20;
    const stepTime = duration / steps;
    const stepVolume = volumeDiff / steps;

    let currentStep = 0;

    const fadeInterval = setInterval(() => {
      currentStep++;
      audio.volume = Math.max(0, Math.min(1, startVolume + (stepVolume * currentStep)));

      if (currentStep >= steps) {
        clearInterval(fadeInterval);
        audio.volume = targetVolume;
      }
    }, stepTime);
  }

  // ========== CONTROL DE VOLUMEN ==========
  public setMusicVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    this.updateState({
      music: {
        ...this.state.music,
        volume: clampedVolume
      }
    });

    if (this.musicAudio) {
      this.musicAudio.volume = clampedVolume * this.state.masterVolume;
    }

    console.log(`🔍 [${this.serviceName}] 🎵 Volumen música: ${clampedVolume}`);
  }

  public setSpeechVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    this.updateState({
      speech: {
        ...this.state.speech,
        volume: clampedVolume
      }
    });

    if (this.speechAudio) {
      this.speechAudio.volume = clampedVolume * this.state.masterVolume;
    }

    console.log(`🔍 [${this.serviceName}] 🗣️ Volumen narración: ${clampedVolume}`);
  }

  public setMasterVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    this.updateState({ masterVolume: clampedVolume });

    // Aplicar a todos los elementos de audio
    if (this.musicAudio) {
      this.musicAudio.volume = this.state.music.volume * clampedVolume;
    }
    if (this.speechAudio) {
      this.speechAudio.volume = this.state.speech.volume * clampedVolume;
    }

    console.log(`🔍 [${this.serviceName}] 🔊 Volumen master: ${clampedVolume}`);
  }

  // ========== MUTE/UNMUTE ==========
  public toggleMute(): boolean {
    const newMuteState = !this.state.isMuted;
    this.setMute(newMuteState);
    return newMuteState;
  }

  public setMute(muted: boolean): void {
    this.updateState({ isMuted: muted });

    if (this.musicAudio) {
      this.musicAudio.muted = muted;
    }
    if (this.speechAudio) {
      this.speechAudio.muted = muted;
    }

    console.log(`ℹ️ [${this.serviceName}] 🔇 Audio ${muted ? 'silenciado' : 'activado'}`);
  }

  // ========== CONTROL GENERAL ==========
  public pauseAll(): void {
    this.pauseMusic();
    this.pauseSpeech();
    console.log(`ℹ️ [${this.serviceName}] ⏸️ Todo el audio pausado`);
  }

  public resumeAll(): void {
    this.resumeMusic();
    this.resumeSpeech();
    console.log(`ℹ️ [${this.serviceName}] ▶️ Todo el audio reanudado`);
  }

  public stopAll(): void {
    this.stopMusic();
    this.stopSpeech();
    console.log(`ℹ️ [${this.serviceName}] ⏹️ Todo el audio detenido`);
  }

  // ========== STATE MANAGEMENT ==========
  private updateState(partialState: Partial<AudioState>): void {
    this.state = { ...this.state, ...partialState };
    this.notifyListeners();
  }

  public getState(): AudioState {
    return { ...this.state };
  }

  public addListener(listener: (state: AudioState) => void): () => void {
    this.listeners.add(listener);

    // Enviar estado actual
    listener(this.getState());

    // Retornar función para remover listener
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getState());
      } catch (error) {
        console.error(`❌ [${this.serviceName}] Error en listener de audio`, error);
      }
    });
  }

  // ========== UTILIDADES ==========
  public isPlaying(type?: AudioType): boolean {
    if (!type) {
      return this.state.music.isPlaying || this.state.speech.isPlaying;
    }

    switch (type) {
      case "music":
        return this.state.music.isPlaying;
      case "speech":
        return this.state.speech.isPlaying;
      default:
        return false;
    }
  }

  public getVolume(type: AudioType): number {
    switch (type) {
      case "music":
        return this.state.music.volume;
      case "speech":
        return this.state.speech.volume;
      case "sfx":
        return this.state.sfx.volume;
      default:
        return 0;
    }
  }

  // ========== CLEANUP ==========
  public destroy(): void {
    this.stopAll();

    if (this.musicAudio) {
      document.body.removeChild(this.musicAudio);
      this.musicAudio = null;
    }

    if (this.speechAudio) {
      document.body.removeChild(this.speechAudio);
      this.speechAudio = null;
    }

    this.listeners.clear();

    console.log(`ℹ️ [${this.serviceName}] 💀 AudioManager destruido`);
  }
}

// ========== SINGLETON EXPORT ==========
export const audioManager = AudioManager.getInstance();
