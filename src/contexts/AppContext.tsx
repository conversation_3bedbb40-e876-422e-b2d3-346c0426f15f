/**
 * ========================================================================
 * AppContext - Contexto Principal de la Aplicación Enygma
 * ========================================================================
 *
 * PROPÓSITO:
 * - Gestiona el estado global de inicialización de la aplicación
 * - Centraliza la configuración de variables de entorno
 * - Maneja errores globales de la aplicación (JavaScript + Promise rejections)
 * - Proporciona utilidades de validación y estado para otros contextos
 *
 * RESPONSABILIDADES:
 * ✅ Configuración: Variables de entorno y APIs externas
 * ✅ Estado de app: Inicialización, estado ready/error, ciclo de vida
 * ✅ Errores globales: Captura, almacenamiento y gestión de errores
 * ✅ Validación: Verificación de configuración crítica para funcionar
 *
 * OPTIMIZACIONES IMPLEMENTADAS:
 * ✅ Memoización del valor del contexto (previene re-renders innecesarios)
 * ✅ Configuración memoizada (variables de entorno se calculan solo una vez)
 * ✅ Funciones estables con useCallback (no cambian referencias)
 * ✅ Dependencias explícitas en hooks (mejor debuggeabilidad)
 *
 * INTEGRACIÓN:
 * - Base fundamental para todos los demás contextos
 * - Debe ser el contexto más externo en la jerarquía
 * - Otros contextos pueden usar isConfigValid() para validar inicialización
 * ========================================================================
 */

import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
  useMemo,
} from "react";

import { generateCorrelationId } from "../services/_helpersService";

// ========== TYPES ==========
/**
 * Configuración principal de la aplicación
 * Contiene todas las URLs y claves de APIs externas necesarias
 */
export interface AppConfig {
  iaApiUrl: string | null;        // URL de la API de IA
  iaApiKey: string | null;        // Clave de autenticación para IA
  speechApiUrl: string | null;    // URL de la API de síntesis de voz
  speechApiKey: string | null;    // Clave para servicio de speech
}

/**
 * Representación de un error capturado por la aplicación
 * Incluye contexto y metadatos para debugging
 */
export interface AppError {
  id: string;           // Identificador único del error
  message: string;      // Mensaje descriptivo del error
  timestamp: Date;      // Momento en que ocurrió el error
  context?: string;     // Contexto donde ocurrió (componente, función, etc.)
  error?: Error;        // Objeto Error original (si disponible)
}

/**
 * Estados posibles de la aplicación durante su ciclo de vida
 */
export type AppState = "initializing" | "ready" | "error";

// ========== INTERFACES ==========
/**
 * Interfaz completa del contexto de aplicación
 * Define todos los valores y métodos disponibles para componentes hijos
 */
interface AppContextProps {
  // ===== ESTADO DE LA APLICACIÓN =====
  appState: AppState;        // Estado actual del ciclo de vida
  isInitialized: boolean;    // Flag de inicialización completada

  // ===== CONFIGURACIÓN =====
  config: AppConfig;         // Configuración completa de la app

  // ===== MANEJO DE ERRORES =====
  errors: AppError[];                                                    // Lista de errores capturados
  addError: (message: string, context?: string, error?: Error) => void;  // Agregar error manualmente
  clearErrors: () => void;                                               // Limpiar todos los errores
  clearError: (id: string) => void;                                      // Limpiar error específico

  // ===== UTILIDADES =====
  isConfigValid: () => boolean;                        // Verificar si la configuración es válida
  getConfigStatus: () => Record<string, boolean>;      // Estado detallado de cada configuración

  // ===== MÉTODOS DE CONTROL =====
  initialize: () => Promise<void>;  // Reinicializar la aplicación
  reset: () => void;                // Reset completo del estado
}

// ========== CONTEXT ==========
const AppContext = createContext<AppContextProps | undefined>(undefined);

/**
 * Hook personalizado para acceder al contexto de aplicación
 * Incluye validación de uso dentro del Provider
 *
 * @returns {AppContextProps} Propiedades y métodos del contexto
 * @throws {Error} Si se usa fuera del AppProvider
 */
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};

/**
 * Provider principal de la aplicación
 * Debe envolver toda la aplicación y ser el contexto más externo
 *
 * @param {Object} props - Props del componente
 * @param {ReactNode} props.children - Componentes hijos
 */
export const AppProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS PRINCIPALES ==========
  const [appState, setAppState] = useState<AppState>("initializing");
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [errors, setErrors] = useState<AppError[]>([]);

  // ========== CONFIGURACIÓN MEMOIZADA ==========
  /**
   * Configuración de la aplicación basada en variables de entorno
   * Se memoiza porque las variables de entorno nunca cambian durante la ejecución
   */
  const config = useMemo<AppConfig>(() => ({
    iaApiUrl: import.meta.env.VITE_IA_API_URL || null,
    iaApiKey: import.meta.env.VITE_IA_API_KEY || null,
    speechApiUrl: import.meta.env.VITE_SPEECH_API_URL || null,
    speechApiKey: import.meta.env.VITE_SPEECH_API_KEY || null,
  }), []); // Array vacío = solo se calcula una vez

  // ========== EFECTOS ==========
  /**
   * Inicialización automática al montar el componente
   */
  useEffect(() => {
    initialize();
  }, []);

  /**
   * Configuración de listeners globales para errores no manejados
   * Captura tanto errores de JavaScript como Promises rechazadas
   */
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      addError(
        `Error global: ${event.message}`,
        `${event.filename}:${event.lineno}:${event.colno}`,
        event.error
      );
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      addError(
        `Promise rechazada: ${event.reason}`,
        "Unhandled Promise Rejection",
        event.reason instanceof Error ? event.reason : undefined
      );
    };

    window.addEventListener("error", handleGlobalError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleGlobalError);
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, []);

  // ========== FUNCIONES MEMOIZADAS ==========
  /**
   * Agregar un error al registro de errores de la aplicación
   * Genera automáticamente ID único y timestamp
   */
  const addError = useCallback((message: string, context?: string, error?: Error) => {
    const appError: AppError = {
      id: generateCorrelationId("error"),
      message,
      timestamp: new Date(),
      context,
      error,
    };
    setErrors((prev) => [...prev, appError]);
  }, []);

  /**
   * Limpiar todos los errores registrados
   */
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  /**
   * Eliminar un error específico por su ID
   */
  const clearError = useCallback((id: string) => {
    setErrors((prev) => prev.filter((error) => error.id !== id));
  }, []);

  /**
   * Verificar si la configuración actual es válida para el funcionamiento
   * Valida que las APIs críticas estén configuradas
   */
  const isConfigValid = useCallback((): boolean => {
    return !!(
      config.iaApiUrl &&
      config.iaApiKey &&
      config.speechApiUrl &&
      config.speechApiKey
    );
  }, [config]);

  /**
   * Obtener el estado detallado de cada elemento de configuración
   * Útil para debugging y pantallas de estado
   */
  const getConfigStatus = useCallback((): Record<string, boolean> => {
    return {
      iaApiUrl: !!config.iaApiUrl,
      iaApiKey: !!config.iaApiKey,
      speechApiUrl: !!config.speechApiUrl,
      speechApiKey: !!config.speechApiKey,
    };
  }, [config]);

  /**
   * Inicializar la aplicación
   * Valida la configuración y establece el estado correspondiente
   */
  const initialize = useCallback(async () => {
    try {
      setAppState("initializing");

      // Validar configuración crítica
      if (!isConfigValid()) {
        throw new Error(
          "Configuración incompleta: faltan variables de entorno críticas"
        );
      }

      setAppState("ready");
      setIsInitialized(true);

      console.log("✅ [AppContext] Aplicación inicializada correctamente", {
        config: getConfigStatus(),
        state: "ready",
        initialized: true,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Error desconocido";
      setAppState("error");
      addError(
        `Error en inicialización: ${errorMessage}`,
        "initialize",
        error instanceof Error ? error : undefined
      );
    }
  }, [isConfigValid, addError]);

  /**
   * Resetear completamente el estado de la aplicación
   * Limpia errores y reinicia el proceso de inicialización
   */
  const reset = useCallback(() => {
    setAppState("initializing");
    setIsInitialized(false);
    clearErrors();
    initialize();
  }, [initialize, clearErrors]);

  // ========== VALOR DEL CONTEXTO MEMOIZADO ==========
  const contextValue = useMemo<AppContextProps>(() => ({
    // Estado
    appState,
    isInitialized,

    // Configuración
    config,

    // Errores
    errors,
    addError,
    clearErrors,
    clearError,

    // Utilidades
    isConfigValid,
    getConfigStatus,

    // Métodos
    initialize,
    reset,
  }), [
    appState,
    isInitialized,
    config,
    errors,
    addError,        // useCallback hace que sea estable
    clearErrors,     // useCallback hace que sea estable
    clearError,      // useCallback hace que sea estable
    isConfigValid,   // useCallback hace que sea estable
    getConfigStatus, // useCallback hace que sea estable
    initialize,      // useCallback hace que sea estable
    reset            // useCallback hace que sea estable
  ]);

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};
